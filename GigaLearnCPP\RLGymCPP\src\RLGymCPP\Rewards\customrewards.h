#pragma once

#include <RLGymCPP/Rewards/Reward.h>
#include <RLGymCPP/CommonValues.h>
#include <RLGymCPP/Gamestates/GameState.h>
#include <map>
#include <algorithm>

class DribbleReward : public RLGC::Reward {
public:
    virtual float GetReward(const RLGC::Player& player, const RLGC::GameState& state, bool isFinal) override {
        const float MIN_BALL_HEIGHT = 109.0f;
        const float MAX_BALL_HEIGHT = 180.0f;
        const float MAX_DISTANCE = 197.0f;
        const float SPEED_MATCH_FACTOR = 2.0f; // Adjust this value to control the importance of speed matching
        const float CAR_MAX_SPEED = RLGC::CommonValues::CAR_MAX_SPEED;

        if (player.isOnGround && state.ball.pos.z >= MIN_BALL_HEIGHT && state.ball.pos.z <= MAX_BALL_HEIGHT && (player.pos - state.ball.pos).Length() < MAX_DISTANCE) {
            float playerSpeed = player.vel.Length();
            float ballSpeed = state.ball.vel.Length();

            // Prevent division by zero if both speeds are zero
            if (playerSpeed + ballSpeed == 0) {
                return 0.0f;
            }

            float speedMatchReward = ((playerSpeed/CAR_MAX_SPEED) + SPEED_MATCH_FACTOR * (1.0f - std::abs(playerSpeed - ballSpeed) / (playerSpeed + ballSpeed))) / 2.0f;
            return speedMatchReward; // Reward for successful dribbling, with a bonus for speed matching, normalized to 1
        } else {
            return 0.0f; // No reward
        }
    }
};

class AerialReward : public RLGC::Reward {
public:
    float minBallHeight;
    int minAirTicks;
    const float MAX_BALL_HEIGHT = 2040.0f; // Ceiling height

    std::map<int, int> airTimeTicks;

    AerialReward(float minBallHeight = 200.0f, int minAirTicks = 12) {
        this->minBallHeight = minBallHeight;
        this->minAirTicks = minAirTicks;
    }

    virtual float GetReward(const RLGC::Player& player, const RLGC::GameState& state, bool isFinal) override {
        if (player.isOnGround) {
            airTimeTicks[player.carId] = 0;
        } else {
            if (airTimeTicks.count(player.carId)) {
                airTimeTicks[player.carId]++;
            } else {
                airTimeTicks[player.carId] = 1;
            }
        }

        if (player.ballTouchedStep) {
            int airTicks = airTimeTicks.count(player.carId) ? airTimeTicks[player.carId] : 0;
            if (airTicks >= this->minAirTicks && state.ball.pos.z > this->minBallHeight) {
                float heightRatio = (state.ball.pos.z - this->minBallHeight) / (MAX_BALL_HEIGHT - this->minBallHeight);
                return std::max(0.f, std::min(1.f, heightRatio)); // Return normalized reward
            }
        }

        return 0.0f;
    }
};